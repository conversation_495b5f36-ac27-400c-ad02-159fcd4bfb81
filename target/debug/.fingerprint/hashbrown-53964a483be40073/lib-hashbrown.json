{"rustc": 12013579709055016942, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 2241668132362809309, "path": 2230384901048184464, "deps": [[5230392855116717286, "equivalent", false, 15180950758188631019], [9150530836556604396, "allocator_api2", false, 861623352437511211], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 5191322065384176142]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-53964a483be40073/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}