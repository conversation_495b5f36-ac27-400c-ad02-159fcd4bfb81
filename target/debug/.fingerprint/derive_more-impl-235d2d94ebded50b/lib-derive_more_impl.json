{"rustc": 12013579709055016942, "features": "[\"default\", \"is_variant\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 11796376952621915773, "profile": 17818141490371658307, "path": 6340491295179711494, "deps": [[373107762698212489, "proc_macro2", false, 2882321119914342069], [17332570067994900305, "syn", false, 7743166133182252504], [17685210698997651194, "convert_case", false, 9203331978303389136], [17990358020177143287, "quote", false, 9504799828398804697]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/derive_more-impl-235d2d94ebded50b/dep-lib-derive_more_impl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}