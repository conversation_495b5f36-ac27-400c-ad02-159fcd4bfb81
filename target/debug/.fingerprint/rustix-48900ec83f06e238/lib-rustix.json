{"rustc": 12013579709055016942, "features": "[\"alloc\", \"std\", \"stdio\", \"termios\"]", "declared_features": "[\"all-apis\", \"alloc\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 8475749685358207080, "path": 15650441399112544794, "deps": [[10004434995811528692, "build_script_build", false, 17406477275866735772], [12846346674781677812, "linux_raw_sys", false, 15656387296344950087], [12848154260885479101, "bitflags", false, 11809960508836270056]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-48900ec83f06e238/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}