{"rustc": 12013579709055016942, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[3430646239657634944, "build_script_build", false, 16827297044496061805]], "local": [{"RerunIfChanged": {"output": "debug/build/rustix-062cf623bc304944/output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_RUSTIX_USE_EXPERIMENTAL_ASM", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_RUSTIX_USE_LIBC", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_USE_LIBC", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_RUSTC_DEP_OF_STD", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_MIRI", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}