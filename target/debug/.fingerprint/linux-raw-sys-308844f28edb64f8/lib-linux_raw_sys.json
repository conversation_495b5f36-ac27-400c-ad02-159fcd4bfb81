{"rustc": 12013579709055016942, "features": "[\"elf\", \"errno\", \"general\", \"ioctl\", \"no_std\"]", "declared_features": "[\"bootparam\", \"btrfs\", \"compiler_builtins\", \"core\", \"default\", \"elf\", \"elf_uapi\", \"errno\", \"general\", \"if_arp\", \"if_ether\", \"if_packet\", \"image\", \"io_uring\", \"ioctl\", \"landlock\", \"loop_device\", \"mempolicy\", \"net\", \"netlink\", \"no_std\", \"prctl\", \"ptrace\", \"rustc-dep-of-std\", \"std\", \"system\", \"xdp\"]", "target": 5772965225213482929, "profile": 8214764587632450424, "path": 986032705803568406, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/linux-raw-sys-308844f28edb64f8/dep-lib-linux_raw_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}