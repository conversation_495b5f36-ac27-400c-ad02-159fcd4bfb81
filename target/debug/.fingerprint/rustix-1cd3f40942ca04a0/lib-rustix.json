{"rustc": 12013579709055016942, "features": "[\"alloc\", \"libc-extra-traits\", \"std\", \"stdio\", \"termios\"]", "declared_features": "[\"all-apis\", \"alloc\", \"cc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"itoa\", \"libc\", \"libc-extra-traits\", \"libc_errno\", \"linux_4_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"once_cell\", \"param\", \"pipe\", \"process\", \"procfs\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 8582226208830198931, "path": 17894333853524851929, "deps": [[3430646239657634944, "build_script_build", false, 3678344478344042328], [5036304442846774733, "linux_raw_sys", false, 7308865087682184142], [12848154260885479101, "bitflags", false, 11809960508836270056]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-1cd3f40942ca04a0/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}