# Rust TUI 应用程序

这是一个使用 Rust 和 ratatui 库创建的终端用户界面（TUI）应用程序。

## 功能特性

- 📋 项目列表管理
- ➕ 添加新项目
- 🗑️ 删除选中项目
- 🎯 键盘导航
- 💬 实时消息反馈
- 🖥️ 仅支持 Windows 编译

## 操作说明

### 正常模式
- `q` - 退出应用程序
- `e` - 进入编辑模式
- `↑/↓` - 选择列表项目
- `Delete` - 删除选中的项目

### 编辑模式
- `Enter` - 添加新项目
- `Esc` - 退出编辑模式
- `Backspace` - 删除输入字符
- 输入文字 - 添加到输入框

## 编译和运行

### 前提条件
- Rust 1.89.0 或更高版本
- Windows 操作系统（仅支持 Windows 编译）

### 编译
```bash
cargo build --release
```

### 运行
```bash
cargo run
```

## 项目结构

```
rust-tui-app/
├── src/
│   └── main.rs          # 主程序文件
├── Cargo.toml           # 项目配置文件
└── README.md           # 项目说明文档
```

## 依赖项

- `ratatui` (0.29.0) - TUI 框架
- `crossterm` (0.29.0) - 跨平台终端操作

## 技术特点

- 使用现代 Rust TUI 库 ratatui
- 响应式布局设计
- 状态管理和事件处理
- 中文界面支持
- 内存安全的 Rust 实现

## 界面布局

应用程序界面分为四个主要区域：

1. **帮助栏** - 显示当前可用的操作提示
2. **项目列表** - 显示所有项目，支持选择和高亮
3. **输入框** - 用于输入新项目名称
4. **消息区** - 显示操作反馈和状态信息

## 开发说明

本项目专门为 Windows 平台设计，在 Cargo.toml 中配置了 Windows 目标平台限制。如需在其他平台运行，请修改相关配置。
